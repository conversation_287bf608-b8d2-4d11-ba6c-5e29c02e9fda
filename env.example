# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_DATABASE=sims
MYSQL_USER=sims
MYSQL_PASSWORD=your_secure_password

# 应用安全配置
SIMS_ENCRYPTION_KEY=32-byte-long-key-for-encryption!
SIMS_JWT_SECRET=your-jwt-secret-key-here

# Redis配置
REDIS_PASSWORD=your_redis_password

# Docker镜像配置
DOCKER_REGISTRY=your-registry.com
IMAGE_TAG=latest

# 部署环境
ENVIRONMENT=production

# SSL证书配置（生产环境）
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 监控配置
ENABLE_MONITORING=true
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000 