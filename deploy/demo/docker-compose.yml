version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: sims-mysql-prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - sims-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10
      interval: 30s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: sims-redis-prod
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 200mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - sims-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 后端API服务
  sims-api:
    image: ${DOCKER_REGISTRY}/${DOCKER_REPOSITORY}/sims-api:${IMAGE_TAG:-latest}
    container_name: sims-api-prod
    restart: unless-stopped
    environment:
      - SIMS_DB_HOST=mysql
      - SIMS_DB_PORT=3306
      - SIMS_DB_USER=${MYSQL_USER}
      - SIMS_DB_PASSWORD=${MYSQL_PASSWORD}
      - SIMS_DB_NAME=${MYSQL_DATABASE}
      - SIMS_ENCRYPTION_KEY=${SIMS_ENCRYPTION_KEY}
      - SIMS_JWT_SECRET=${SIMS_JWT_SECRET}
      - SIMS_REDIS_HOST=redis
      - SIMS_REDIS_PORT=6379
      - SIMS_REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sims-network
    volumes:
      - api_data:/app/data
      - /data/sims/api/config.json:/app/config.json:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 前端Web服务
  sims-web:
    image: ${DOCKER_REGISTRY}/${DOCKER_REPOSITORY}/sims-web:${IMAGE_TAG:-latest}
    container_name: sims-web-prod
    restart: unless-stopped
    depends_on:
      - sims-api
    networks:
      - sims-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: sims-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    # depends_on:
    #   sims-web:
    #     condition: service_healthy
    #   sims-api:
    #     condition: service_healthy
    networks:
      - sims-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

volumes:
  mysql_data:
    driver: local
  api_data:
    driver: local
  redis_data:
    driver: local

networks:
  sims-network:
    driver: bridge 