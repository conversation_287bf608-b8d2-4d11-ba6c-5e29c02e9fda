{"name": "web", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.1.4+sha1.2432063d815cfa88fd9fef1d85a445e3f609851d", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "vue-tsc -b --noEmit"}, "dependencies": {"@internationalized/date": "^3.8.1", "@tailwindcss/vite": "^4.1.8", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.3.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-vue-next": "^0.511.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "reka-ui": "^2.3.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.2", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.2.5", "vue-sonner": "^2.0.0", "zod": "^3.25.42"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@types/node": "^22.15.29", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}