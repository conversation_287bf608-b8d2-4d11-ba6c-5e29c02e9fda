@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --warning: oklch(0.734 0.194 83.21);  
  --warning-foreground: oklch(0.145 0 0);
  --success: oklch(0.699 0.195 142.95);
  --success-foreground: oklch(0.985 0 0);
  --info: oklch(0.557 0.229 260.116);
  --info-foreground: oklch(0.985 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.145 0 0);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  --warning: oklch(0.734 0.194 83.21);
  --warning-foreground: oklch(0.145 0 0);
  --success: oklch(0.699 0.195 142.95);
  --success-foreground: oklch(0.145 0 0);
  --info: oklch(0.665 0.178 257.5);
  --info-foreground: oklch(0.145 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  button {
    @apply cursor-pointer;
  }

  [data-slot="select-item"] {
    @apply cursor-pointer!;
  }

  /* 确保图片在暗色模式下的适配 */
  .dark img {
    filter: brightness(0.8) contrast(1.2);
  }

  .dark img.no-filter {
    filter: none;
  }
}

@layer components {
  /* 自定义工具类 */
  .text-warning {
    color: hsl(var(--warning));
  }
  
  .text-warning-foreground {
    color: hsl(var(--warning-foreground));
  }
  
  .bg-warning {
    background-color: hsl(var(--warning));
  }
  
  .bg-warning-foreground {
    background-color: hsl(var(--warning-foreground));
  }
  
  .text-success {
    color: hsl(var(--success));
  }
  
  .text-success-foreground {
    color: hsl(var(--success-foreground));
  }
  
  .bg-success {
    background-color: hsl(var(--success));
  }
  
  .bg-success-foreground {
    background-color: hsl(var(--success-foreground));
  }
  
  .text-info {
    color: hsl(var(--info));
  }
  
  .text-info-foreground {
    color: hsl(var(--info-foreground));
  }
  
  .bg-info {
    background-color: hsl(var(--info));
  }
  
  .bg-info-foreground {
    background-color: hsl(var(--info-foreground));
  }

  /* 渐变背景适配 */
  .gradient-bg-light {
    @apply bg-gradient-to-br from-slate-50 to-slate-100;
  }
  
  .dark .gradient-bg-light {
    @apply bg-gradient-to-br from-slate-900 to-slate-800;
  }
  
  .gradient-bg-primary {
    @apply bg-gradient-to-br from-primary/10 to-primary/5;
  }
  
  .dark .gradient-bg-primary {
    @apply bg-gradient-to-br from-primary/20 to-primary/10;
  }

  /* 卡片阴影适配 */
  .card-shadow {
    @apply shadow-sm;
  }
  
  .dark .card-shadow {
    @apply shadow-lg shadow-black/10;
  }

  /* 状态指示器 */
  .status-active {
    @apply bg-success text-success-foreground;
  }
  
  .status-warning {
    @apply bg-warning text-warning-foreground;
  }
    
  .status-error {
    @apply bg-destructive text-destructive-foreground;
  }
  
  .status-info {
    @apply bg-info text-info-foreground;
  }

  /* 代码块适配 */
  .code-block {
    @apply bg-muted/50 text-foreground border border-border rounded-lg p-4 font-mono text-sm;
  }
  
  .inline-code {
    @apply bg-muted/80 text-foreground px-1.5 py-0.5 rounded text-sm font-mono;
  }

  /* 输入框聚焦状态 */
  .input-focus {
    @apply focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }

  /* 过渡动画 */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
}

@layer utilities {
  /* 暗色模式特定工具类 */
  .dark-hidden {
    @apply dark:hidden;
  }
  
  .dark-block {
    @apply hidden dark:block;
  }
  
  .light-hidden {
    @apply hidden dark:block;
  }
  
  .light-block {
    @apply block dark:hidden;
  }
}