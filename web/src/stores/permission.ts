import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { permissionAPI } from '@/api'
import { useAuthStore } from './auth'

// 权限缓存接口
interface PermissionCache {
  [key: string]: boolean
}

// 菜单权限配置
export interface MenuPermission {
  resource: string
  action: string
}

// 按钮权限配置
export interface ButtonPermission {
  resource: string
  action: string
  label?: string
}

// 权限检查状态
interface PermissionState {
  [key: string]: 'checking' | 'loaded'
}

export const usePermissionStore = defineStore('permission', () => {
  const authStore = useAuthStore()

  // 权限缓存
  const permissionCache = ref<PermissionCache>({})
  // 权限检查状态，避免重复请求
  const permissionStates = ref<PermissionState>({})
  const loading = ref(false)

  /**
   * 生成权限缓存键
   */
  function generateCacheKey(role: string, resource: string, action: string): string {
    return `${role}:${resource}:${action}`
  }

  /**
   * 统一的权限检查入口（已废弃，保留用于兼容性）
   * @param resource 资源
   * @param action 操作
   * @param fallbackValue 缓存为空时的回退值，默认为 false
   * @returns 是否有权限
   * @deprecated 使用 hasPermissionSync 替代
   */
  async function hasPermission(resource: string, action: string, fallbackValue = false): Promise<boolean> {
    return hasPermissionSync(resource, action, fallbackValue)
  }

  /**
   * 同步权限检查（仅使用缓存）
   * @param resource 资源
   * @param action 操作
   * @param fallbackValue 缓存为空时的回退值，默认为 false
   * @returns 是否有权限
   */
  function hasPermissionSync(resource: string, action: string, fallbackValue = false): boolean {
    const userRole = authStore.user?.role
    if (!userRole) {
      return false
    }

    // 超级管理员拥有所有权限
    if (userRole === 'super_admin') {
      return true
    }

    const cacheKey = generateCacheKey(userRole, resource, action)
    const cached = permissionCache.value[cacheKey]

    return cached !== undefined ? cached : fallbackValue
  }

  /**
   * 批量权限检查（已废弃，保留用于兼容性）
   * @param permissions 权限列表
   * @returns 权限结果映射
   * @deprecated 使用 initializePermissions 替代
   */
  async function checkMultiplePermissions(permissions: Array<{ resource: string, action: string }>): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {}

    // 同步检查所有权限
    permissions.forEach((permission) => {
      const key = `${permission.resource}:${permission.action}`
      const hasAccess = hasPermissionSync(permission.resource, permission.action)
      results[key] = hasAccess
    })

    return results
  }

  /**
   * 初始化用户权限缓存
   * 在用户登录、页面刷新、应用初始化时调用
   */
  async function initializePermissions(): Promise<void> {
    const userRole = authStore.user?.role
    if (!userRole) {
      clearCache()
      return
    }

    // 超级管理员无需初始化权限缓存
    if (userRole === 'super_admin') {
      return
    }

    try {
      loading.value = true
      const response = await permissionAPI.getUserAllPermissions()
      const permissions = response.data?.permissions || {}

      // 清空现有缓存
      clearRoleCache(userRole)

      // 更新权限缓存
      Object.entries(permissions).forEach(([key, hasAccess]) => {
        const cacheKey = generateCacheKey(userRole, ...key.split(':') as [string, string])
        permissionCache.value[cacheKey] = hasAccess
        permissionStates.value[cacheKey] = 'loaded'
      })

      console.log(`权限缓存初始化完成，用户角色: ${userRole}，权限数量: ${Object.keys(permissions).length}`)
    }
    catch (error) {
      console.error('权限缓存初始化失败:', error)
      // 初始化失败时清空缓存，避免使用过期数据
      clearRoleCache(userRole)
    }
    finally {
      loading.value = false
    }
  }

  /**
   * 预加载权限（已废弃，保留用于兼容性）
   * @param permissions 权限列表
   * @deprecated 权限现在在初始化时一次性加载，无需预加载
   */
  async function preloadPermissions(permissions: Array<{ resource: string, action: string }>) {
    // 权限现在在初始化时一次性加载，此方法保留用于兼容性
    console.warn('preloadPermissions 已废弃，权限在初始化时一次性加载')
  }

  /**
   * 检查菜单权限
   * @param menuPermission 菜单权限配置
   * @param useAsync 是否使用异步检查
   * @returns 是否有权限
   */
  function checkMenuPermission(menuPermission: MenuPermission, useAsync = false): boolean | Promise<boolean> {
    if (useAsync) {
      return hasPermission(menuPermission.resource, menuPermission.action)
    }
    return hasPermissionSync(menuPermission.resource, menuPermission.action)
  }

  /**
   * 检查按钮权限
   * @param buttonPermission 按钮权限配置
   * @param useAsync 是否使用异步检查
   * @returns 是否有权限
   */
  function checkButtonPermission(buttonPermission: ButtonPermission, useAsync = false): boolean | Promise<boolean> {
    if (useAsync) {
      return hasPermission(buttonPermission.resource, buttonPermission.action)
    }
    return hasPermissionSync(buttonPermission.resource, buttonPermission.action)
  }

  /**
   * 清除权限缓存
   */
  function clearCache() {
    permissionCache.value = {}
    permissionStates.value = {}
  }

  /**
   * 清除特定用户角色的缓存
   * @param role 用户角色
   */
  function clearRoleCache(role: string) {
    Object.keys(permissionCache.value).forEach((key) => {
      if (key.startsWith(`${role}:`)) {
        delete permissionCache.value[key]
        delete permissionStates.value[key]
      }
    })
  }

  // 计算属性：当前用户角色
  const currentRole = computed(() => authStore.user?.role)
  const isAdmin = computed(() => currentRole.value === 'super_admin')
  const isSecurityManager = computed(() => currentRole.value === 'sec_mgr')
  const isDeveloper = computed(() => currentRole.value === 'dev')
  const isAuditor = computed(() => currentRole.value === 'auditor')

  return {
    permissionCache,
    loading,
    hasPermission,
    hasPermissionSync,
    checkMultiplePermissions,
    checkMenuPermission,
    checkButtonPermission,
    preloadPermissions,
    initializePermissions,
    clearCache,
    clearRoleCache,
    currentRole,
    isAdmin,
    isSecurityManager,
    isDeveloper,
    isAuditor,
  }
})
